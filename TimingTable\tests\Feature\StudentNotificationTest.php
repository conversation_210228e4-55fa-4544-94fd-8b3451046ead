<?php

namespace Tests\Feature;

use App\Models\Api\Main\Notification;
use App\Models\Api\Core\Key;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StudentNotificationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test notification creation methods
     */
    public function test_notification_creation_methods()
    {
        // Create a key first
        $key = Key::create([
            'value' => 'test-key-123',
            'status' => 'used',
            'keyable_type' => 'student',
            'keyable_id' => 1,
        ]);

        // Create test user with key_id
        $studentUser1 = User::create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'key_id' => $key->id,
        ]);

        // Test session data
        $sessionData = [
            'lesson_id' => 1,
            'teacher_name' => 'John Doe',
            'module_name' => 'Mathematics',
            'session_type' => 'course',
            'day' => 'mon',
            'day_name' => 'Monday',
            'start_time' => '08:00',
            'end_time' => '10:00',
            'classroom' => 'A101',
            'group_id' => 1,
            'section_id' => null,
        ];

        // Test creating notification for a single student
        $notification = Notification::createNewSessionNotification($studentUser1->id, $sessionData);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $studentUser1->id,
            'type' => 'new_session_scheduled',
            'title' => 'New Session Scheduled',
            'read' => false,
        ]);

        // Check notification content
        $this->assertStringContainsString('John Doe', $notification->message);
        $this->assertStringContainsString('Mathematics', $notification->message);
        $this->assertStringContainsString('COURSE', $notification->message);
        $this->assertStringContainsString('Monday', $notification->message);
        $this->assertStringContainsString('A101', $notification->message);

        // Check notification data
        $this->assertEquals(1, $notification->data['lesson_id']);
        $this->assertEquals('John Doe', $notification->data['teacher_name']);
        $this->assertEquals('Mathematics', $notification->data['module_name']);
        $this->assertEquals('course', $notification->data['session_type']);
    }

    /**
     * Test that students can fetch their notifications
     */
    public function test_students_can_fetch_notifications()
    {
        // Create a key first
        $key = Key::create([
            'value' => 'test-key-456',
            'status' => 'used',
            'keyable_type' => 'student',
            'keyable_id' => 2,
        ]);

        $studentUser = User::create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'key_id' => $key->id,
        ]);

        // Create some notifications
        Notification::createForUser(
            $studentUser->id,
            'new_session_scheduled',
            'New Session Scheduled',
            'A new course session has been scheduled.',
            ['lesson_id' => 1]
        );

        // Authenticate as student
        $this->actingAs($studentUser, 'sanctum');

        // Fetch notifications
        $response = $this->getJson('/api/notifications');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'type',
                    'title',
                    'message',
                    'data',
                    'read',
                    'created_at'
                ]
            ]
        ]);

        // Test unread count
        $response = $this->getJson('/api/notifications-unread-count');
        $response->assertStatus(200);
        $response->assertJson(['count' => 1]);
    }

    /**
     * Test marking notifications as read
     */
    public function test_mark_notifications_as_read()
    {
        // Create a key first
        $key = Key::create([
            'value' => 'test-key-789',
            'status' => 'used',
            'keyable_type' => 'student',
            'keyable_id' => 3,
        ]);

        $studentUser = User::create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'key_id' => $key->id,
        ]);

        // Create a notification
        $notification = Notification::createForUser(
            $studentUser->id,
            'new_session_scheduled',
            'New Session Scheduled',
            'A new course session has been scheduled.',
            ['lesson_id' => 1]
        );

        // Authenticate as student
        $this->actingAs($studentUser, 'sanctum');

        // Mark as read
        $response = $this->patchJson("/api/notifications/{$notification->id}/read");
        $response->assertStatus(200);

        // Check that notification is marked as read
        $this->assertDatabaseHas('notifications', [
            'id' => $notification->id,
            'read' => true,
        ]);

        // Test unread count should be 0 now
        $response = $this->getJson('/api/notifications-unread-count');
        $response->assertStatus(200);
        $response->assertJson(['count' => 0]);
    }
}
