"use client";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Calendar, MapP<PERSON>, User, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import Modal, { openModal } from "../../global/Modal/Modal"
import { useEffect, useState } from "react"
import {
    getNotifications,
    getUnreadNotificationsCount,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    deleteNotification,
    type NotificationResponse
} from "@/lib/server/actions/notification/notificationActions"
import { useCurrentUser } from "@/lib/hooks/useCurrentUser"
import Button from "../../global/Buttons/Button"

export default function StudentNotifications() {
    const { user, loading: userLoading } = useCurrentUser()
    const [notifications, setNotifications] = useState<NotificationResponse[]>([])
    const [unreadCount, setUnreadCount] = useState(0)
    const [loading, setLoading] = useState(false)

    const fetchNotifications = async () => {
        try {
            setLoading(true)
            const [notificationsData, countData] = await Promise.all([
                getNotifications(1),
                getUnreadNotificationsCount()
            ])

            setNotifications(notificationsData.data)
            setUnreadCount(countData)
        } catch (error) {
            console.error('Error fetching notifications:', error)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        // Only fetch notifications if user is a student
        if (user && user.key.keyable_type === 'student') {
            fetchNotifications()
        }
    }, [user])

    const handleMarkAsRead = async (notificationId: number) => {
        try {
            await markNotificationAsRead(notificationId)
            await fetchNotifications() // Refresh the list
        } catch (error) {
            console.error('Error marking notification as read:', error)
        }
    }

    const handleMarkAllAsRead = async () => {
        try {
            await markAllNotificationsAsRead()
            await fetchNotifications() // Refresh the list
        } catch (error) {
            console.error('Error marking all notifications as read:', error)
        }
    }

    const handleDeleteNotification = async (notificationId: number) => {
        try {
            await deleteNotification(notificationId)
            await fetchNotifications() // Refresh the list
        } catch (error) {
            console.error('Error deleting notification:', error)
        }
    }

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case 'new_session_scheduled':
                return <Calendar size={20} className="text-blue-500" />
            default:
                return <Bell size={20} className="text-blue-500" />
        }
    }

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        })
    }

    const formatSessionType = (type: string) => {
        switch (type?.toLowerCase()) {
            case 'td':
                return 'TD (Tutorial)'
            case 'tp':
                return 'TP (Practical)'
            case 'course':
                return 'Course (Lecture)'
            default:
                return type?.toUpperCase() || 'Session'
        }
    }

    // Only show notifications if user is actually a student
    if (userLoading) {
        return (
            <div className="relative">
                <Bell className="text-gray-400" size={24} />
            </div>
        )
    }

    if (!user || user.key.keyable_type !== 'student') {
        return null // Don't show student notifications for non-students
    }

    return (
        <>
            <div className="relative">
                <Bell
                    className="text-primary dark:text-dark-primary cursor-pointer"
                    size={24}
                    onClick={() => openModal("student-notifications-modal")}
                />
                {unreadCount > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {unreadCount}
                    </span>
                )}
            </div>
            <Modal id="student-notifications-modal">
                <div className="flex flex-col gap-4 h-[70vh] w-2/3 max-w-4xl">
                    <div className="flex justify-between items-center">
                        <h2 className="text-xl font-bold text-on-surface dark:text-dark-on-surface">
                            Session Notifications ({unreadCount} unread)
                        </h2>
                        {unreadCount > 0 && (
                            <Button
                                mode="outlined"
                                onClick={handleMarkAllAsRead}
                                icon={<Check size={16} />}
                            >
                                Mark All Read
                            </Button>
                        )}
                    </div>
                    
                    {loading ? (
                        <div className="flex items-center justify-center h-32">
                            <Clock className="animate-spin" size={24} />
                            <span className="ml-2">Loading notifications...</span>
                        </div>
                    ) : notifications.length === 0 ? (
                        <div className="flex items-center justify-center h-32 text-gray-500">
                            No notifications
                        </div>
                    ) : (
                        <ul className="flex flex-col gap-3 overflow-y-auto p-4 border rounded-lg">
                            {notifications.map((notification) => (
                                <li 
                                    key={notification.id} 
                                    className={`flex items-start gap-3 p-4 rounded-lg shadow ${
                                        notification.read 
                                            ? 'bg-gray-50 dark:bg-gray-800' 
                                            : 'bg-surface-container dark:bg-dark-surface-container border-l-4 border-blue-500'
                                    }`}
                                >
                                    <div className="flex-shrink-0 mt-1">
                                        {getNotificationIcon(notification.type)}
                                    </div>
                                    
                                    <div className="flex-grow">
                                        <div className="flex justify-between items-start">
                                            <h3 className="font-semibold text-lg">
                                                {notification.title}
                                            </h3>
                                            <span className="text-xs text-gray-500">
                                                {formatDate(notification.created_at)}
                                            </span>
                                        </div>
                                        
                                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                            {notification.message}
                                        </p>
                                        
                                        {notification.data && notification.type === 'new_session_scheduled' && (
                                            <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                                                <div className="flex items-center gap-2">
                                                    <User size={14} className="text-gray-500" />
                                                    <span><strong>Teacher:</strong> {notification.data.teacher_name}</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <BookOpen size={14} className="text-gray-500" />
                                                    <span><strong>Subject:</strong> {notification.data.module_name}</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Calendar size={14} className="text-gray-500" />
                                                    <span><strong>Type:</strong> {formatSessionType(notification.data.session_type)}</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Clock size={14} className="text-gray-500" />
                                                    <span><strong>Time:</strong> {notification.data.day_name} {notification.data.start_time}-{notification.data.end_time}</span>
                                                </div>
                                                <div className="flex items-center gap-2 col-span-2">
                                                    <MapPin size={14} className="text-gray-500" />
                                                    <span><strong>Classroom:</strong> {notification.data.classroom}</span>
                                                </div>
                                            </div>
                                        )}
                                        
                                        <div className="flex gap-2 mt-3">
                                            {!notification.read && (
                                                <Button
                                                    mode="outlined"
                                                    onClick={() => handleMarkAsRead(notification.id)}
                                                    icon={<Check size={14} />}
                                                >
                                                    Mark Read
                                                </Button>
                                            )}
                                            <Button
                                                mode="outlined"
                                                onClick={() => handleDeleteNotification(notification.id)}
                                                icon={<Trash2 size={14} />}
                                            >
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </Modal>
        </>
    )
}
