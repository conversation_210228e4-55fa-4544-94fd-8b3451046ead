import { getLocale, getTranslations } from "next-intl/server";
import NavBarGroup from "../../global/Navigations/NavBar/NavBarGroupd";
import NavBarItem from "../../global/Navigations/NavBar/NavBarItem";
import UpBar from "../../global/Navigations/NavBar/NavBar";
import Mode from "../Mode";
import Request from "./Request";
import TeacherNotifications from "./TeacherNotifications";
import StudentNotifications from "./StudentNotifications";
import { getUser } from "@/lib/server/actions/auth/getUser";

export default async function UpBarDash() {
    const t = await getTranslations()
    const locale = await getLocale()
    const user = await getUser()
    const userType = user.user?.key?.keyable_type



    return (
        <UpBar>
            <NavBarGroup>
                <NavBarItem link={`/${locale}/`}>{t('Dashboard.UpBar.Leave')}</NavBarItem>
                <NavBarItem link={`/${locale}/dashboard`}>{t('Dashboard.UpBar.Home')}</NavBarItem>
                <div
                    className="flex gap-2 h-full items-center"
                >
                    <Mode />
                    {/* Show admin requests only for admin users */}
                    {userType === 'admin' && <Request />}
                    {/* Show teacher notifications only for teacher users */}
                    {userType === 'teacher' && <TeacherNotifications />}
                    {/* Show student notifications only for student users */}
                    {userType === 'student' && <StudentNotifications />}
                </div>
            </NavBarGroup>
        </UpBar>
    )
}