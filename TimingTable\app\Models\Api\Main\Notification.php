<?php

namespace App\Models\Api\Main;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'read' => 'boolean',
        'read_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeUnread($query)
    {
        return $query->where('read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('read', true);
    }

    public function markAsRead()
    {
        $this->update([
            'read' => true,
            'read_at' => now(),
        ]);
    }

    public static function createForUser($userId, $type, $title, $message, $data = null)
    {
        return static::create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
        ]);
    }

    public static function createRequestApproved($teacherUserId, $requestId, $lessonId = null)
    {
        return static::createForUser(
            $teacherUserId,
            'request_approved',
            'Request Approved',
            'Your classroom request has been approved and the lesson has been created.',
            [
                'request_id' => $requestId,
                'lesson_id' => $lessonId,
            ]
        );
    }

    public static function createRequestRejected($teacherUserId, $requestId, $reason = null)
    {
        return static::createForUser(
            $teacherUserId,
            'request_rejected',
            'Request Rejected',
            $reason ?: 'Your classroom request has been rejected.',
            [
                'request_id' => $requestId,
            ]
        );
    }

    /**
     * Create notification for students when a new session is scheduled
     */
    public static function createNewSessionNotification($studentUserId, $sessionData)
    {
        $teacherName = $sessionData['teacher_name'];
        $moduleName = $sessionData['module_name'];
        $sessionType = strtoupper($sessionData['session_type']);
        $dayName = $sessionData['day_name'];
        $startTime = $sessionData['start_time'];
        $endTime = $sessionData['end_time'];
        $classroom = $sessionData['classroom'];

        return static::createForUser(
            $studentUserId,
            'new_session_scheduled',
            'New Session Scheduled',
            "A new {$sessionType} session for {$moduleName} has been scheduled by {$teacherName} on {$dayName} from {$startTime} to {$endTime} in {$classroom}.",
            [
                'lesson_id' => $sessionData['lesson_id'],
                'teacher_name' => $teacherName,
                'module_name' => $moduleName,
                'session_type' => $sessionData['session_type'],
                'day' => $sessionData['day'],
                'day_name' => $dayName,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'classroom' => $classroom,
                'group_id' => $sessionData['group_id'] ?? null,
                'section_id' => $sessionData['section_id'] ?? null,
            ]
        );
    }

    /**
     * Create notifications for all students in a group
     */
    public static function createNotificationsForGroup($groupId, $sessionData)
    {
        $group = \App\Models\Api\Main\Group::with(['students.key.user'])->find($groupId);

        if (!$group) {
            return [];
        }

        $notifications = [];
        foreach ($group->students as $student) {
            if ($student->key && $student->key->user) {
                $notifications[] = static::createNewSessionNotification(
                    $student->key->user->id,
                    $sessionData
                );
            }
        }

        return $notifications;
    }

    /**
     * Create notifications for all students in a section
     */
    public static function createNotificationsForSection($sectionId, $sessionData)
    {
        $section = \App\Models\Api\Main\Section::with(['groups.students.key.user'])->find($sectionId);

        if (!$section) {
            return [];
        }

        $notifications = [];
        foreach ($section->groups as $group) {
            foreach ($group->students as $student) {
                if ($student->key && $student->key->user) {
                    $notifications[] = static::createNewSessionNotification(
                        $student->key->user->id,
                        $sessionData
                    );
                }
            }
        }

        return $notifications;
    }
}
