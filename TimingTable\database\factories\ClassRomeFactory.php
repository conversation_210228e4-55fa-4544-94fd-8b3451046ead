<?php

namespace Database\Factories;

use App\Models\Api\Core\ClassRome;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClassRomeFactory extends Factory
{
    protected $model = ClassRome::class;

    public function definition(): array
    {
        return [
            'number' => $this->faker->unique()->regexify('A[0-9]{3}'),
            'capacity' => $this->faker->numberBetween(20, 100),
        ];
    }
}
