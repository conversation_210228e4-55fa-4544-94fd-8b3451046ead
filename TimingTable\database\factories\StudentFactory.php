<?php

namespace Database\Factories;

use App\Models\Api\Users\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

class StudentFactory extends Factory
{
    protected $model = Student::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->firstName(),
            'last' => $this->faker->lastName(),
            'username' => $this->faker->unique()->userName(),
            'date_of_birth' => $this->faker->date(),
            'inscreption_number' => $this->faker->unique()->numerify('STU######'),
            'baladiyas_id' => null,
            'group_id' => 1, // Will be overridden in tests
        ];
    }
}
