<?php

namespace Database\Factories;

use App\Models\Api\Users\Teacher;
use Illuminate\Database\Eloquent\Factories\Factory;

class TeacherFactory extends Factory
{
    protected $model = Teacher::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->firstName(),
            'last' => $this->faker->lastName(),
            'username' => $this->faker->unique()->userName(),
            'date_of_birth' => $this->faker->date(),
            'grade' => $this->faker->randomElement(['Assistant', 'Associate Professor', 'Professor']),
            'research_field' => $this->faker->sentence(3),
        ];
    }
}
