<?php

namespace Database\Factories;

use App\Models\Api\Users\Admin;
use Illuminate\Database\Eloquent\Factories\Factory;

class AdminFactory extends Factory
{
    protected $model = Admin::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->firstName(),
            'last' => $this->faker->lastName(),
            'username' => $this->faker->unique()->userName(),
            'date_of_birth' => $this->faker->date(),
        ];
    }
}
